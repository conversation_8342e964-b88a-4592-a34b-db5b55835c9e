#!/usr/bin/env python3
"""
<PERSON><PERSON>t to visualize prediction confidence matrix for uploaded music files.
This shows how confident the model is about each genre for a specific uploaded file.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from backend.ml_model.model import predict_genre, GENRES

def visualize_prediction_confidence(audio_file_path, save_plot=True):
    """
    Create a confidence matrix visualization for a single audio file prediction.
    
    Args:
        audio_file_path (str): Path to the audio file
        save_plot (bool): Whether to save the plot as an image
    
    Returns:
        dict: Prediction result with all probabilities
    """
    
    if not os.path.exists(audio_file_path):
        print(f"Audio file not found: {audio_file_path}")
        return None
    
    print(f"Analyzing: {audio_file_path}")
    
    # Get prediction with all probabilities
    prediction_result = predict_genre(audio_file_path)
    
    if not prediction_result or 'all_probabilities' not in prediction_result:
        print("Failed to get prediction or all_probabilities not available")
        return None
    
    # Extract probabilities for visualization
    genres = [item['genre'] for item in prediction_result['all_probabilities']]
    confidences = [item['confidence'] for item in prediction_result['all_probabilities']]
    
    # Create the visualization
    plt.figure(figsize=(12, 8))
    
    # Create a horizontal bar chart
    colors = plt.cm.viridis(np.linspace(0, 1, len(genres)))
    bars = plt.barh(genres, confidences, color=colors)
    
    # Customize the plot
    plt.xlabel('Confidence Score', fontsize=12)
    plt.ylabel('Music Genres', fontsize=12)
    plt.title(f'Genre Prediction Confidence\nFile: {os.path.basename(audio_file_path)}\nTop Prediction: {prediction_result["genre"]} ({prediction_result["confidence"]:.1%})', 
              fontsize=14, pad=20)
    
    # Add confidence values on bars
    for bar, confidence in zip(bars, confidences):
        plt.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                f'{confidence:.1%}', ha='left', va='center', fontsize=10)
    
    # Add a vertical line at the top prediction confidence
    top_confidence = prediction_result['confidence']
    plt.axvline(x=top_confidence, color='red', linestyle='--', alpha=0.7, 
                label=f'Top Prediction: {top_confidence:.1%}')
    
    plt.legend()
    plt.grid(axis='x', alpha=0.3)
    plt.tight_layout()
    
    # Save the plot if requested
    if save_plot:
        output_dir = os.path.join("backend", "ml_model", "prediction_visualizations")
        os.makedirs(output_dir, exist_ok=True)
        
        # Create filename based on audio file name
        audio_filename = os.path.splitext(os.path.basename(audio_file_path))[0]
        output_path = os.path.join(output_dir, f"confidence_{audio_filename}.png")
        
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Confidence visualization saved to: {output_path}")
    
    # Show the plot
    plt.show()
    
    return prediction_result

def create_confidence_heatmap(audio_file_path, save_plot=True):
    """
    Create a heatmap-style confidence matrix for a single prediction.
    
    Args:
        audio_file_path (str): Path to the audio file
        save_plot (bool): Whether to save the plot as an image
    """
    
    if not os.path.exists(audio_file_path):
        print(f"Audio file not found: {audio_file_path}")
        return None
    
    # Get prediction with all probabilities
    prediction_result = predict_genre(audio_file_path)
    
    if not prediction_result or 'all_probabilities' not in prediction_result:
        print("Failed to get prediction or all_probabilities not available")
        return None
    
    # Extract probabilities and reshape for heatmap
    confidences = [item['confidence'] for item in prediction_result['all_probabilities']]
    confidence_matrix = np.array(confidences).reshape(1, -1)
    
    # Create the heatmap
    plt.figure(figsize=(14, 3))
    
    sns.heatmap(confidence_matrix, 
                annot=True, 
                fmt='.1%', 
                cmap='YlOrRd', 
                xticklabels=GENRES,
                yticklabels=[f'Prediction for\n{os.path.basename(audio_file_path)}'],
                cbar_kws={'label': 'Confidence Score'})
    
    plt.title(f'Genre Prediction Confidence Heatmap\nTop Prediction: {prediction_result["genre"]} ({prediction_result["confidence"]:.1%})', 
              fontsize=14, pad=20)
    plt.xlabel('Music Genres', fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Save the plot if requested
    if save_plot:
        output_dir = os.path.join("backend", "ml_model", "prediction_visualizations")
        os.makedirs(output_dir, exist_ok=True)
        
        # Create filename based on audio file name
        audio_filename = os.path.splitext(os.path.basename(audio_file_path))[0]
        output_path = os.path.join(output_dir, f"heatmap_{audio_filename}.png")
        
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Confidence heatmap saved to: {output_path}")
    
    # Show the plot
    plt.show()
    
    return prediction_result

def analyze_recent_upload():
    """
    Analyze the most recent uploaded file in the media/uploads directory.
    """
    uploads_dir = os.path.join("backend", "media", "uploads")
    
    if not os.path.exists(uploads_dir):
        print(f"Uploads directory not found: {uploads_dir}")
        return
    
    # Get all audio files in uploads directory
    audio_extensions = ['.wav', '.mp3', '.flac', '.m4a', '.aac']
    audio_files = []
    
    for file in os.listdir(uploads_dir):
        if any(file.lower().endswith(ext) for ext in audio_extensions):
            file_path = os.path.join(uploads_dir, file)
            audio_files.append((file_path, os.path.getmtime(file_path)))
    
    if not audio_files:
        print("No audio files found in uploads directory")
        return
    
    # Sort by modification time and get the most recent
    audio_files.sort(key=lambda x: x[1], reverse=True)
    most_recent_file = audio_files[0][0]
    
    print(f"Analyzing most recent upload: {most_recent_file}")
    
    # Create both visualizations
    print("\n=== Creating Bar Chart Visualization ===")
    visualize_prediction_confidence(most_recent_file)
    
    print("\n=== Creating Heatmap Visualization ===")
    create_confidence_heatmap(most_recent_file)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Analyze specific file
        audio_file = sys.argv[1]
        print(f"Analyzing specific file: {audio_file}")
        
        print("\n=== Creating Bar Chart Visualization ===")
        visualize_prediction_confidence(audio_file)
        
        print("\n=== Creating Heatmap Visualization ===")
        create_confidence_heatmap(audio_file)
    else:
        # Analyze most recent upload
        analyze_recent_upload()
