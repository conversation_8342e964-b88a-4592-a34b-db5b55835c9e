#!/usr/bin/env python3
"""
Test script to check if the API is returning the all_probabilities field
"""

import os
import json
import requests
from pathlib import Path

def test_api_response():
    """Test the API response to see if all_probabilities is included"""
    
    # Find an uploaded file to test with
    uploads_dir = os.path.join("backend", "media", "uploads")
    
    if not os.path.exists(uploads_dir):
        print(f"❌ Uploads directory not found: {uploads_dir}")
        return
    
    # Get the first audio file
    audio_extensions = ['.wav', '.mp3', '.flac', '.m4a', '.aac']
    test_file = None
    
    for file in os.listdir(uploads_dir):
        if any(file.lower().endswith(ext) for ext in audio_extensions):
            test_file = os.path.join(uploads_dir, file)
            break
    
    if not test_file:
        print("❌ No audio files found in uploads directory")
        print("Please upload a music file through the web interface first.")
        return
    
    print(f"🎵 Testing with file: {os.path.basename(test_file)}")
    
    # Test the model directly (not through API)
    print("\n1. Testing model directly...")
    try:
        from backend.ml_model.model import predict_genre
        result = predict_genre(test_file)
        
        if result:
            print(f"✅ Model prediction successful")
            print(f"   Genre: {result.get('genre', 'N/A')}")
            print(f"   Confidence: {result.get('confidence', 0):.1%}")
            
            if 'all_probabilities' in result:
                print(f"✅ all_probabilities field present with {len(result['all_probabilities'])} genres")
                
                # Show top 3 probabilities
                sorted_probs = sorted(result['all_probabilities'], 
                                    key=lambda x: x['confidence'], reverse=True)[:3]
                print("   Top 3 probabilities:")
                for prob in sorted_probs:
                    print(f"     {prob['genre']}: {prob['confidence']:.1%}")
            else:
                print("❌ all_probabilities field missing from model response")
        else:
            print("❌ Model prediction failed")
            
    except Exception as e:
        print(f"❌ Error testing model: {e}")
    
    # Test the API endpoint
    print("\n2. Testing API endpoint...")
    try:
        # Check if server is running
        response = requests.get("http://localhost:8000/api/genres/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
            
            # Test file upload
            with open(test_file, 'rb') as f:
                files = {'file': f}
                response = requests.post("http://localhost:8000/api/upload/", files=files, timeout=30)
                
                if response.status_code == 201:
                    api_result = response.json()
                    print("✅ API upload successful")
                    print(f"   Genre: {api_result.get('genre', 'N/A')}")
                    print(f"   Confidence: {api_result.get('confidence', 0):.1%}")
                    
                    if 'all_probabilities' in api_result:
                        print(f"✅ all_probabilities field present in API response")
                        print(f"   Contains {len(api_result['all_probabilities'])} genres")
                    else:
                        print("❌ all_probabilities field missing from API response")
                        print("Available fields:", list(api_result.keys()))
                else:
                    print(f"❌ API upload failed: {response.status_code}")
                    print(f"Response: {response.text}")
        else:
            print(f"❌ Backend server not responding: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server")
        print("Please start the backend server with: cd backend && python manage.py runserver")
    except Exception as e:
        print(f"❌ Error testing API: {e}")

def check_frontend_files():
    """Check if frontend files have been updated correctly"""
    
    print("\n3. Checking frontend files...")
    
    # Check Results.jsx
    results_file = "frontend/src/components/Results.jsx"
    if os.path.exists(results_file):
        with open(results_file, 'r') as f:
            content = f.read()
            if 'all_probabilities' in content:
                print("✅ Results.jsx contains all_probabilities handling")
            else:
                print("❌ Results.jsx missing all_probabilities handling")
                
            if 'confidence-matrix' in content:
                print("✅ Results.jsx contains confidence-matrix component")
            else:
                print("❌ Results.jsx missing confidence-matrix component")
    else:
        print(f"❌ Results.jsx not found at {results_file}")
    
    # Check Results.css
    css_file = "frontend/src/components/Results.css"
    if os.path.exists(css_file):
        with open(css_file, 'r') as f:
            content = f.read()
            if 'confidence-matrix' in content:
                print("✅ Results.css contains confidence-matrix styles")
            else:
                print("❌ Results.css missing confidence-matrix styles")
    else:
        print(f"❌ Results.css not found at {css_file}")

if __name__ == "__main__":
    print("🔍 TESTING CONFIDENCE MATRIX IMPLEMENTATION")
    print("=" * 50)
    
    test_api_response()
    check_frontend_files()
    
    print("\n" + "=" * 50)
    print("TROUBLESHOOTING STEPS:")
    print("1. Make sure backend server is running: cd backend && python manage.py runserver")
    print("2. Make sure frontend is running: cd frontend && npm run dev")
    print("3. Clear browser cache and refresh the page")
    print("4. Check browser console for any JavaScript errors")
