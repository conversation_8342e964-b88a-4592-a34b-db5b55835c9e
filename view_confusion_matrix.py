#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to view the confusion matrix generated by the model training.
"""

import os
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from pathlib import Path

def view_confusion_matrix():
    """Display the confusion matrix image if it exists"""
    
    # Path to the confusion matrix image
    cm_path = os.path.join("backend", "ml_model", "confusion_matrix.png")
    
    if not os.path.exists(cm_path):
        print(f"Confusion matrix not found at {cm_path}")
        print("Please run train_model.py first to generate the confusion matrix.")
        return
    
    try:
        # Load and display the image
        img = mpimg.imread(cm_path)
        
        plt.figure(figsize=(12, 10))
        plt.imshow(img)
        plt.axis('off')  # Hide axes
        plt.title('Music Genre Classification - Confusion Matrix', fontsize=16, pad=20)
        plt.tight_layout()
        plt.show()
        
        print(f"Confusion matrix displayed from: {cm_path}")
        
    except Exception as e:
        print(f"Error displaying confusion matrix: {e}")

if __name__ == "__main__":
    view_confusion_matrix()
