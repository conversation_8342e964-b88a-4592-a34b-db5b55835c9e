import './Results.css';

const Results = ({ result }) => {
  const { genre, confidence, top_predictions, all_probabilities } = result;

  // Debug logging
  console.log("🔍 Results component received:", result);
  console.log("📊 all_probabilities:", all_probabilities);
  console.log("📈 Has all_probabilities:", !!all_probabilities);
  console.log("📏 Number of probabilities:", all_probabilities?.length || 0);

  // More detailed debugging
  if (all_probabilities && all_probabilities.length > 0) {
    console.log("✅ CONFIDENCE MATRIX DATA AVAILABLE");
    console.log("First 3 probabilities:", all_probabilities.slice(0, 3));
  } else {
    console.log("❌ NO CONFIDENCE MATRIX DATA");
    console.log("Available result keys:", Object.keys(result));
  }

  // Format confidence as percentage
  const confidencePercent = (confidence * 100).toFixed(2);

  // Determine confidence level class
  let confidenceClass = 'low';
  if (confidence >= 0.7) {
    confidenceClass = 'high';
  } else if (confidence >= 0.4) {
    confidenceClass = 'medium';
  }

  return (
    <div className="results-container">
      <h2>Classification Results</h2>

      <div className="result-card">
        <div className="genre-result">
          <h3>Predicted Genre</h3>
          <div className="genre-name">{genre}</div>
        </div>

        <div className="confidence-result">
          <h3>Confidence</h3>
          <div className={`confidence-value ${confidenceClass}`}>
            {confidencePercent}%
          </div>
          <div className="confidence-bar-container">
            <div
              className={`confidence-bar ${confidenceClass}`}
              style={{ width: `${confidencePercent}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Show top predictions if available */}
      {top_predictions && top_predictions.length > 1 && (
        <div className="top-predictions">
          <h3>Alternative Genres</h3>
          <div className="predictions-list">
            {top_predictions.slice(1).map((pred, index) => (
              <div key={index} className="prediction-item">
                <span className="prediction-genre">{pred.genre}</span>
                <div className="prediction-confidence-container">
                  <div
                    className="prediction-confidence-bar"
                    style={{ width: `${(pred.confidence * 100).toFixed(2)}%` }}
                  ></div>
                  <span className="prediction-confidence-value">
                    {(pred.confidence * 100).toFixed(2)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Debug info for troubleshooting */}
      <div style={{background: '#444', padding: '10px', margin: '10px 0', borderRadius: '5px', fontSize: '12px'}}>
        <strong>Debug Info:</strong><br/>
        Has all_probabilities: {all_probabilities ? 'YES' : 'NO'}<br/>
        Length: {all_probabilities?.length || 0}<br/>
        Available keys: {Object.keys(result).join(', ')}
      </div>

      {/* Show confidence matrix if all probabilities are available */}
      {all_probabilities && Array.isArray(all_probabilities) && all_probabilities.length > 0 ? (
        <div className="confidence-matrix">
          <h3>Confidence Matrix - All Genres</h3>
          <div className="matrix-container">
            {all_probabilities
              .sort((a, b) => b.confidence - a.confidence) // Sort by confidence descending
              .map((prob, index) => {
                const probPercent = (prob.confidence * 100).toFixed(1);
                const isTopPrediction = prob.genre === genre;

                return (
                  <div
                    key={index}
                    className={`matrix-row ${isTopPrediction ? 'top-prediction' : ''}`}
                  >
                    <div className="matrix-genre">{prob.genre}</div>
                    <div className="matrix-bar-container">
                      <div
                        className={`matrix-bar ${isTopPrediction ? 'top-bar' : ''}`}
                        style={{
                          width: `${probPercent}%`,
                          backgroundColor: isTopPrediction
                            ? '#4CAF50'
                            : `rgba(76, 175, 80, ${prob.confidence})`
                        }}
                      ></div>
                      <span className="matrix-percentage">{probPercent}%</span>
                    </div>
                  </div>
                );
              })}
          </div>
          <div className="matrix-explanation">
            <p>
              This shows the model's confidence for each genre.
              The <strong>green bar</strong> represents the predicted genre.
            </p>
          </div>
        </div>
      ) : (
        <div className="confidence-matrix">
          <h3>Confidence Matrix - Debug</h3>
          <div style={{background: '#444', padding: '10px', borderRadius: '5px'}}>
            <p>❌ Confidence matrix data not available</p>
            <p>all_probabilities: {JSON.stringify(all_probabilities)}</p>
            <p>Type: {typeof all_probabilities}</p>
            <p>Is Array: {Array.isArray(all_probabilities) ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}

      <div className="result-explanation">
        <p>
          The model predicts this audio is <strong>{genre}</strong> music with
          a confidence of <strong>{confidencePercent}%</strong>.
        </p>
        <p className="consistency-note">
          <strong>Note:</strong> This model now provides consistent predictions for the same audio file.
        </p>
      </div>
    </div>
  );
};

export default Results;
