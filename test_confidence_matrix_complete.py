#!/usr/bin/env python3
"""
Complete test to verify the confidence matrix functionality works end-to-end
"""

import requests
import json
import os
import time

def test_backend_api():
    """Test the backend API directly"""
    print("🔍 TESTING BACKEND API")
    print("=" * 50)
    
    # Find a test file
    uploads_dir = 'backend/media/uploads'
    test_file = None
    
    for file in os.listdir(uploads_dir):
        if file.endswith(('.mp3', '.wav')):
            test_file = os.path.join(uploads_dir, file)
            break
    
    if not test_file:
        print("❌ No test file found")
        return False
    
    print(f"📁 Testing with: {os.path.basename(test_file)}")
    
    try:
        # Test API
        with open(test_file, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/api/upload/', files=files, timeout=30)
            
            if response.status_code == 201:
                data = response.json()
                print("✅ API Response successful")
                print(f"🎵 Genre: {data.get('genre', 'N/A')}")
                print(f"📊 Confidence: {data.get('confidence', 0):.1%}")
                
                if 'all_probabilities' in data and len(data['all_probabilities']) == 10:
                    print("✅ all_probabilities field present with 10 genres")
                    
                    # Show confidence matrix
                    print("\n📈 CONFIDENCE MATRIX:")
                    print("-" * 50)
                    sorted_probs = sorted(data['all_probabilities'], 
                                        key=lambda x: x['confidence'], reverse=True)
                    
                    for i, prob in enumerate(sorted_probs):
                        genre = prob['genre']
                        confidence = prob['confidence']
                        bar_length = int(confidence * 50)
                        bar = "█" * bar_length + "░" * (50 - bar_length)
                        
                        if genre == data['genre']:
                            print(f"🎯 {i+1:2}. {genre:10} │{bar}│ {confidence:.1%}")
                        else:
                            print(f"   {i+1:2}. {genre:10} │{bar}│ {confidence:.1%}")
                    
                    return True
                else:
                    print("❌ all_probabilities field missing or incomplete")
                    print(f"Available keys: {list(data.keys())}")
                    return False
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_frontend_proxy():
    """Test if frontend proxy is working"""
    print("\n🌐 TESTING FRONTEND PROXY")
    print("=" * 50)
    
    try:
        # Test if frontend can reach backend through proxy
        response = requests.get('http://localhost:5173/api/genres/', timeout=10)
        if response.status_code == 200:
            print("✅ Frontend proxy working")
            genres = response.json().get('genres', [])
            print(f"📋 Available genres: {', '.join(genres)}")
            return True
        else:
            print(f"❌ Proxy error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend proxy error: {e}")
        return False

def create_test_html():
    """Create a simple test HTML file to verify the confidence matrix"""
    print("\n📄 CREATING TEST HTML FILE")
    print("=" * 50)
    
    # Sample data that should show confidence matrix
    sample_data = {
        "genre": "metal",
        "confidence": 0.5276,
        "top_predictions": [
            {"genre": "metal", "confidence": 0.5276},
            {"genre": "hiphop", "confidence": 0.2781},
            {"genre": "pop", "confidence": 0.0669}
        ],
        "all_probabilities": [
            {"genre": "blues", "confidence": 0.0007},
            {"genre": "classical", "confidence": 0.0017},
            {"genre": "country", "confidence": 0.0050},
            {"genre": "disco", "confidence": 0.0584},
            {"genre": "hiphop", "confidence": 0.2781},
            {"genre": "jazz", "confidence": 0.0033},
            {"genre": "metal", "confidence": 0.5276},
            {"genre": "pop", "confidence": 0.0669},
            {"genre": "reggae", "confidence": 0.0133},
            {"genre": "rock", "confidence": 0.0450}
        ]
    }
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confidence Matrix Test</title>
    <style>
        body {{ font-family: Arial, sans-serif; background: #1a1a1a; color: white; padding: 20px; }}
        .confidence-matrix {{ background: #333; padding: 20px; border-radius: 8px; margin: 20px 0; }}
        .matrix-container {{ display: flex; flex-direction: column; gap: 8px; }}
        .matrix-row {{ display: flex; align-items: center; padding: 8px; background: #3a3a3a; border-radius: 4px; }}
        .matrix-row.top-prediction {{ background: rgba(76, 175, 80, 0.2); border: 1px solid #4caf50; }}
        .matrix-genre {{ min-width: 80px; font-weight: bold; margin-right: 16px; }}
        .matrix-bar-container {{ display: flex; align-items: center; flex: 1; gap: 8px; }}
        .matrix-bar {{ height: 12px; border-radius: 6px; min-width: 2px; }}
        .matrix-percentage {{ min-width: 50px; text-align: right; font-weight: bold; }}
    </style>
</head>
<body>
    <h1>🎵 Music Genre Confidence Matrix Test</h1>
    
    <div class="result-card">
        <h2>Predicted Genre: {sample_data['genre']} ({sample_data['confidence']:.1%})</h2>
    </div>
    
    <div class="confidence-matrix">
        <h3>Confidence Matrix - All Genres</h3>
        <div class="matrix-container">
"""
    
    # Sort probabilities by confidence (descending)
    sorted_probs = sorted(sample_data['all_probabilities'], 
                         key=lambda x: x['confidence'], reverse=True)
    
    for prob in sorted_probs:
        genre = prob['genre']
        confidence = prob['confidence']
        prob_percent = confidence * 100
        is_top = genre == sample_data['genre']
        
        bg_color = '#4CAF50' if is_top else f'rgba(76, 175, 80, {confidence})'
        row_class = 'matrix-row top-prediction' if is_top else 'matrix-row'
        
        html_content += f"""
            <div class="{row_class}">
                <div class="matrix-genre">{genre}</div>
                <div class="matrix-bar-container">
                    <div class="matrix-bar" style="width: {prob_percent:.1f}%; background-color: {bg_color};"></div>
                    <span class="matrix-percentage">{prob_percent:.1f}%</span>
                </div>
            </div>"""
    
    html_content += """
        </div>
        <div style="margin-top: 16px; padding: 8px; background: rgba(76, 175, 80, 0.1); border-radius: 4px; border-left: 3px solid #4caf50;">
            <p>This shows the model's confidence for each genre. The <strong>green bar</strong> represents the predicted genre.</p>
        </div>
    </div>
    
    <div style="background: #444; padding: 16px; border-radius: 8px; margin: 20px 0;">
        <h3>✅ Test Results:</h3>
        <p>If you can see the confidence matrix above with bars for all 10 genres, then the styling and logic are working correctly.</p>
        <p>The issue might be that the frontend is not receiving the <code>all_probabilities</code> data from the API.</p>
    </div>
    
</body>
</html>
"""
    
    with open('confidence_matrix_test.html', 'w') as f:
        f.write(html_content)
    
    print("✅ Test HTML file created: confidence_matrix_test.html")
    print("🌐 Open this file in your browser to see how the confidence matrix should look")

def main():
    """Run all tests"""
    print("🎵 COMPLETE CONFIDENCE MATRIX TEST")
    print("=" * 60)
    
    # Test 1: Backend API
    backend_ok = test_backend_api()
    
    # Test 2: Frontend proxy
    frontend_ok = test_frontend_proxy()
    
    # Test 3: Create visual test
    create_test_html()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY:")
    print(f"Backend API: {'✅ Working' if backend_ok else '❌ Failed'}")
    print(f"Frontend Proxy: {'✅ Working' if frontend_ok else '❌ Failed'}")
    print("Visual Test: ✅ Created (confidence_matrix_test.html)")
    
    if backend_ok and not frontend_ok:
        print("\n🔧 RECOMMENDATION:")
        print("The backend is working correctly, but the frontend proxy has issues.")
        print("Try restarting the frontend server or check the vite.config.js proxy settings.")
    elif backend_ok and frontend_ok:
        print("\n🔧 RECOMMENDATION:")
        print("Both backend and frontend are working. The issue might be:")
        print("1. Browser cache - try hard refresh (Ctrl+Shift+R)")
        print("2. React component not re-rendering - check browser console for errors")
        print("3. CSS styling issues - check if confidence-matrix styles are loaded")

if __name__ == "__main__":
    main()
