#!/usr/bin/env python3
"""
Test script to demonstrate the new confidence matrix functionality.
This script shows how to get and display confidence scores for all genres.
"""

import os
import json
from backend.ml_model.model import predict_genre

def test_confidence_matrix():
    """Test the confidence matrix functionality with uploaded files"""
    
    # Check for uploaded files
    uploads_dir = os.path.join("backend", "media", "uploads")
    
    if not os.path.exists(uploads_dir):
        print(f"Uploads directory not found: {uploads_dir}")
        print("Please upload some music files through the web interface first.")
        return
    
    # Get all audio files
    audio_extensions = ['.wav', '.mp3', '.flac', '.m4a', '.aac']
    audio_files = []
    
    for file in os.listdir(uploads_dir):
        if any(file.lower().endswith(ext) for ext in audio_extensions):
            file_path = os.path.join(uploads_dir, file)
            audio_files.append(file_path)
    
    if not audio_files:
        print("No audio files found in uploads directory")
        print("Please upload some music files through the web interface first.")
        return
    
    print(f"Found {len(audio_files)} audio files. Testing confidence matrix...")
    print("=" * 60)
    
    # Test the first few files
    for i, audio_file in enumerate(audio_files[:3]):  # Test first 3 files
        print(f"\n{i+1}. Testing: {os.path.basename(audio_file)}")
        print("-" * 40)
        
        # Get prediction with all probabilities
        result = predict_genre(audio_file)
        
        if result and 'all_probabilities' in result:
            print(f"Top Prediction: {result['genre']} ({result['confidence']:.1%})")
            print("\nAll Genre Confidences:")
            
            # Sort by confidence (highest first)
            sorted_probs = sorted(result['all_probabilities'], 
                                key=lambda x: x['confidence'], reverse=True)
            
            for prob in sorted_probs:
                genre = prob['genre']
                confidence = prob['confidence']
                bar_length = int(confidence * 50)  # Scale to 50 characters
                bar = "█" * bar_length + "░" * (50 - bar_length)
                
                # Highlight the top prediction
                if genre == result['genre']:
                    print(f"  🎯 {genre:10} │{bar}│ {confidence:.1%}")
                else:
                    print(f"     {genre:10} │{bar}│ {confidence:.1%}")
            
            print(f"\nAPI Response Preview:")
            print(json.dumps({
                'genre': result['genre'],
                'confidence': result['confidence'],
                'all_probabilities': result['all_probabilities'][:3]  # Show first 3
            }, indent=2))
            
        else:
            print("❌ Failed to get prediction or all_probabilities not available")
        
        print("=" * 60)

def show_api_example():
    """Show example of what the API now returns"""
    
    print("\n" + "=" * 60)
    print("API RESPONSE EXAMPLE")
    print("=" * 60)
    
    example_response = {
        "genre": "pop",
        "confidence": 0.89,
        "top_predictions": [
            {"genre": "pop", "confidence": 0.89},
            {"genre": "rock", "confidence": 0.07},
            {"genre": "disco", "confidence": 0.03}
        ],
        "all_probabilities": [
            {"genre": "blues", "confidence": 0.001},
            {"genre": "classical", "confidence": 0.002},
            {"genre": "country", "confidence": 0.005},
            {"genre": "disco", "confidence": 0.03},
            {"genre": "hiphop", "confidence": 0.008},
            {"genre": "jazz", "confidence": 0.004},
            {"genre": "metal", "confidence": 0.001},
            {"genre": "pop", "confidence": 0.89},
            {"genre": "reggae", "confidence": 0.002},
            {"genre": "rock", "confidence": 0.07}
        ]
    }
    
    print("When you upload music and it shows '90% pop', the API now returns:")
    print(json.dumps(example_response, indent=2))
    
    print("\n" + "=" * 60)
    print("FRONTEND DISPLAY")
    print("=" * 60)
    print("The web interface will now show:")
    print("1. Main prediction: Pop (89%)")
    print("2. Alternative genres: Rock (7%), Disco (3%)")
    print("3. 📊 CONFIDENCE MATRIX - showing ALL 10 genres with their confidence scores")
    print("   - Pop: ████████████████████████████████████████████████ 89.0%")
    print("   - Rock: ███░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ 7.0%")
    print("   - Disco: █░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ 3.0%")
    print("   - Country: ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ 0.5%")
    print("   - ... (and 6 more genres)")

if __name__ == "__main__":
    print("🎵 MUSIC GENRE CONFIDENCE MATRIX TESTER 🎵")
    print("This script demonstrates the new confidence matrix functionality.")
    
    # Test with actual files
    test_confidence_matrix()
    
    # Show API example
    show_api_example()
    
    print("\n" + "=" * 60)
    print("HOW TO USE:")
    print("=" * 60)
    print("1. Upload music through your web interface")
    print("2. The result will now show confidence for ALL genres")
    print("3. Use prediction_confidence_visualizer.py for detailed charts")
    print("4. The web interface automatically displays the confidence matrix")
