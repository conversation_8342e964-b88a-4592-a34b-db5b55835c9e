import axios from 'axios';

// Base URL for API requests
const API_URL = '/api';

// Get list of available genres
export const getGenres = async () => {
  try {
    const response = await axios.get(`${API_URL}/genres/`);
    return response.data.genres;
  } catch (error) {
    console.error('Error fetching genres:', error);
    throw error;
  }
};

// Upload audio file and get prediction
export const classifyAudio = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await axios.post(`${API_URL}/upload/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    // Debug logging
    console.log("🌐 API Response received:", response.data);
    console.log("🔍 Has all_probabilities in API response:", 'all_probabilities' in response.data);

    return response.data;
  } catch (error) {
    console.error('Error classifying audio:', error);
    throw error;
  }
};
