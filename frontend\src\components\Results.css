.results-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 1.5rem;
  background-color: #2a2a2a;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.results-container h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #fff;
  text-align: center;
}

.results-container h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #aaa;
}

.result-card {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.genre-result, .confidence-result {
  padding: 1rem;
  background-color: #333;
  border-radius: 8px;
}

.genre-name {
  font-size: 2rem;
  font-weight: bold;
  color: #fff;
  text-transform: capitalize;
}

.confidence-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.confidence-value.high {
  color: #4caf50;
}

.confidence-value.medium {
  color: #ff9800;
}

.confidence-value.low {
  color: #f44336;
}

.confidence-bar-container {
  width: 100%;
  height: 10px;
  background-color: #444;
  border-radius: 5px;
  overflow: hidden;
}

.confidence-bar {
  height: 100%;
  border-radius: 5px;
}

.confidence-bar.high {
  background-color: #4caf50;
}

.confidence-bar.medium {
  background-color: #ff9800;
}

.confidence-bar.low {
  background-color: #f44336;
}

/* Top predictions styles */
.top-predictions {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #333;
  border-radius: 8px;
}

.predictions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.prediction-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background-color: #3a3a3a;
  border-radius: 4px;
}

.prediction-genre {
  font-weight: bold;
  color: #fff;
  text-transform: capitalize;
}

.prediction-confidence-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 60%;
}

.prediction-confidence-bar {
  height: 8px;
  background-color: #4caf50;
  border-radius: 4px;
}

.prediction-confidence-value {
  min-width: 60px;
  text-align: right;
  font-size: 0.9rem;
  color: #ddd;
}

.result-explanation {
  margin-top: 1.5rem;
  text-align: center;
  color: #ddd;
}

.consistency-note {
  margin-top: 1rem;
  font-size: 0.9rem;
  color: #aaa;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 0.75rem;
  border-radius: 4px;
  border-left: 3px solid #4caf50;
}

/* Confidence Matrix Styles */
.confidence-matrix {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #333;
  border-radius: 8px;
}

.confidence-matrix h3 {
  color: #fff;
  margin-bottom: 1rem;
  text-align: center;
}

.matrix-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.matrix-row {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background-color: #3a3a3a;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.matrix-row.top-prediction {
  background-color: rgba(76, 175, 80, 0.2);
  border: 1px solid #4caf50;
}

.matrix-genre {
  min-width: 80px;
  font-weight: bold;
  color: #fff;
  text-transform: capitalize;
  margin-right: 1rem;
}

.matrix-bar-container {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 0.5rem;
}

.matrix-bar {
  height: 12px;
  border-radius: 6px;
  transition: width 0.3s ease;
  min-width: 2px;
}

.matrix-bar.top-bar {
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
}

.matrix-percentage {
  min-width: 50px;
  text-align: right;
  font-size: 0.9rem;
  color: #ddd;
  font-weight: bold;
}

.matrix-explanation {
  text-align: center;
  color: #aaa;
  font-size: 0.9rem;
  padding: 0.5rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 4px;
  border-left: 3px solid #4caf50;
}
