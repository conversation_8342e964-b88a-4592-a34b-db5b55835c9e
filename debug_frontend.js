// Debug script to check if confidence matrix data is being received
// Paste this into your browser console when viewing the results

console.log("🔍 DEBUGGING CONFIDENCE MATRIX");

// Check if the result object contains all_probabilities
function debugResult(result) {
    console.log("📊 Result object:", result);
    
    if (result.all_probabilities) {
        console.log("✅ all_probabilities found!");
        console.log("📈 Number of genres:", result.all_probabilities.length);
        console.log("🎯 Top 3 probabilities:");
        
        const sorted = result.all_probabilities
            .sort((a, b) => b.confidence - a.confidence)
            .slice(0, 3);
            
        sorted.forEach((prob, index) => {
            console.log(`   ${index + 1}. ${prob.genre}: ${(prob.confidence * 100).toFixed(1)}%`);
        });
    } else {
        console.log("❌ all_probabilities NOT found in result");
        console.log("Available fields:", Object.keys(result));
    }
}

// Check if confidence matrix element exists in DOM
function checkDOM() {
    const matrix = document.querySelector('.confidence-matrix');
    if (matrix) {
        console.log("✅ Confidence matrix element found in DOM");
        console.log("📏 Matrix rows:", matrix.querySelectorAll('.matrix-row').length);
    } else {
        console.log("❌ Confidence matrix element NOT found in DOM");
        console.log("Available result elements:", 
            Array.from(document.querySelectorAll('[class*="result"]')).map(el => el.className)
        );
    }
}

// Run checks
console.log("Run these commands after uploading a file:");
console.log("1. debugResult(yourResultObject) - to check the data");
console.log("2. checkDOM() - to check if the matrix appears in the page");

// Auto-check DOM
checkDOM();
