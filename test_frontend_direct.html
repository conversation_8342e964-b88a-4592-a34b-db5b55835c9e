<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Test - Confidence Matrix</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: white; padding: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #333; padding: 20px; margin: 20px 0; border-radius: 8px; }
        button { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; }
        button:hover { background: #45a049; }
        .result { background: #444; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .confidence-matrix { background: #333; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .matrix-container { display: flex; flex-direction: column; gap: 8px; }
        .matrix-row { display: flex; align-items: center; padding: 8px; background: #3a3a3a; border-radius: 4px; }
        .matrix-row.top-prediction { background: rgba(76, 175, 80, 0.2); border: 1px solid #4caf50; }
        .matrix-genre { min-width: 80px; font-weight: bold; margin-right: 16px; }
        .matrix-bar-container { display: flex; align-items: center; flex: 1; gap: 8px; }
        .matrix-bar { height: 12px; border-radius: 6px; min-width: 2px; }
        .matrix-percentage { min-width: 50px; text-align: right; font-weight: bold; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Frontend Confidence Matrix Test</h1>
        
        <div class="test-section">
            <h2>1. Test Backend API</h2>
            <button onclick="testBackendAPI()">Test Backend API</button>
            <div id="backend-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. Test Frontend Proxy</h2>
            <button onclick="testFrontendProxy()">Test Frontend Proxy</button>
            <div id="proxy-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. Simulate Upload</h2>
            <button onclick="simulateUpload()">Simulate File Upload</button>
            <div id="upload-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>4. Test Confidence Matrix Rendering</h2>
            <button onclick="renderConfidenceMatrix()">Render Test Matrix</button>
            <div id="matrix-container"></div>
        </div>
    </div>

    <script>
        async function testBackendAPI() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '🔄 Testing backend API...';
            
            try {
                const response = await fetch('http://localhost:8000/api/genres/');
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `✅ Backend API working<br>Genres: ${data.genres.join(', ')}`;
                } else {
                    resultDiv.innerHTML = `❌ Backend API error: ${response.status}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Backend API error: ${error.message}`;
            }
        }
        
        async function testFrontendProxy() {
            const resultDiv = document.getElementById('proxy-result');
            resultDiv.innerHTML = '🔄 Testing frontend proxy...';
            
            try {
                const response = await fetch('/api/genres/');
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `✅ Frontend proxy working<br>Genres: ${data.genres.join(', ')}`;
                } else {
                    resultDiv.innerHTML = `❌ Frontend proxy error: ${response.status}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Frontend proxy error: ${error.message}`;
            }
        }
        
        async function simulateUpload() {
            const resultDiv = document.getElementById('upload-result');
            resultDiv.innerHTML = '🔄 Simulating file upload...';
            
            // Create a fake FormData (this won't actually work without a real file)
            resultDiv.innerHTML = `
                <p>⚠️ Cannot simulate actual file upload from this test page.</p>
                <p>To test upload:</p>
                <ol>
                    <li>Go to your React app (http://localhost:5173)</li>
                    <li>Upload a music file</li>
                    <li>Check browser console for debug logs</li>
                    <li>Look for confidence matrix on the page</li>
                </ol>
            `;
        }
        
        function renderConfidenceMatrix() {
            const container = document.getElementById('matrix-container');
            
            // Sample data that should be returned by the API
            const sampleData = {
                genre: "metal",
                confidence: 0.5276,
                all_probabilities: [
                    {genre: "blues", confidence: 0.0007},
                    {genre: "classical", confidence: 0.0017},
                    {genre: "country", confidence: 0.0050},
                    {genre: "disco", confidence: 0.0584},
                    {genre: "hiphop", confidence: 0.2781},
                    {genre: "jazz", confidence: 0.0033},
                    {genre: "metal", confidence: 0.5276},
                    {genre: "pop", confidence: 0.0669},
                    {genre: "reggae", confidence: 0.0133},
                    {genre: "rock", confidence: 0.0450}
                ]
            };
            
            // Sort by confidence
            const sortedProbs = sampleData.all_probabilities.sort((a, b) => b.confidence - a.confidence);
            
            let matrixHTML = `
                <div class="confidence-matrix">
                    <h3>🎯 Confidence Matrix - All Genres</h3>
                    <div class="matrix-container">
            `;
            
            sortedProbs.forEach(prob => {
                const probPercent = (prob.confidence * 100).toFixed(1);
                const isTopPrediction = prob.genre === sampleData.genre;
                const backgroundColor = isTopPrediction 
                    ? '#4CAF50' 
                    : `rgba(76, 175, 80, ${Math.max(prob.confidence, 0.1)})`;
                
                matrixHTML += `
                    <div class="matrix-row ${isTopPrediction ? 'top-prediction' : ''}">
                        <div class="matrix-genre">${prob.genre}</div>
                        <div class="matrix-bar-container">
                            <div class="matrix-bar" style="width: ${probPercent}%; background-color: ${backgroundColor};"></div>
                            <span class="matrix-percentage">${probPercent}%</span>
                        </div>
                    </div>
                `;
            });
            
            matrixHTML += `
                    </div>
                    <div style="margin-top: 16px; padding: 8px; background: rgba(76, 175, 80, 0.1); border-radius: 4px; border-left: 3px solid #4caf50;">
                        <p>This shows the model's confidence for each genre. The <strong>green bar</strong> represents the predicted genre.</p>
                    </div>
                </div>
            `;
            
            container.innerHTML = matrixHTML;
        }
        
        // Auto-run some tests on page load
        window.onload = function() {
            console.log('🧪 Frontend test page loaded');
            renderConfidenceMatrix();
        };
    </script>
</body>
</html>
