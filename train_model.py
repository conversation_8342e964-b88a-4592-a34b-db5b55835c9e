import os
import numpy as np
import librosa
import pickle
import time
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import classification_report, confusion_matrix
import glob
import matplotlib.pyplot as plt
import seaborn as sns

# List of genres
GENRES = ['blues', 'classical', 'country', 'disco', 'hiphop', 'jazz', 'metal', 'pop', 'reggae', 'rock']

def extract_features(file_path):
    """Extract features from an audio file with a simpler approach"""
    try:
        # Check if file exists
        if not os.path.isfile(file_path):
            print(f"File does not exist: {file_path}")
            return None

        print(f"Loading audio file: {file_path}")
        # Load audio file with more robust error handling
        try:
            y, sr = librosa.load(file_path, sr=22050, mono=True, duration=30)
        except Exception as load_error:
            print(f"Error loading audio file {file_path}: {load_error}")
            # Try with different backend
            import warnings
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                try:
                    import soundfile as sf
                    y, sr = sf.read(file_path)
                    # Convert to mono if needed
                    if len(y.shape) > 1:
                        y = np.mean(y, axis=1)
                    # Resample if needed
                    if sr != 22050:
                        y = librosa.resample(y, orig_sr=sr, target_sr=22050)
                        sr = 22050
                except Exception as sf_error:
                    print(f"SoundFile also failed: {sf_error}")
                    return None

        # Check if audio data is valid
        if len(y) == 0:
            print(f"Empty audio data for {file_path}")
            return None

        # Extract MFCCs
        mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
        mfccs_mean = np.mean(mfccs.T, axis=0)

        # Extract Spectral Centroid
        spectral_centroid = librosa.feature.spectral_centroid(y=y, sr=sr)
        spectral_centroid_mean = np.mean(spectral_centroid.T, axis=0)

        # Extract Zero Crossing Rate
        zero_crossing_rate = librosa.feature.zero_crossing_rate(y)
        zero_crossing_rate_mean = np.mean(zero_crossing_rate.T, axis=0)

        # Extract Chroma Features
        chroma = librosa.feature.chroma_stft(y=y, sr=sr)
        chroma_mean = np.mean(chroma.T, axis=0)

        # Extract Spectral Contrast
        spectral_contrast = librosa.feature.spectral_contrast(y=y, sr=sr)
        spectral_contrast_mean = np.mean(spectral_contrast.T, axis=0)

        # Combine all features
        features = np.concatenate((
            mfccs_mean,
            spectral_centroid_mean,
            zero_crossing_rate_mean,
            chroma_mean,
            spectral_contrast_mean
        ))

        return features

    except Exception as e:
        print(f"Error extracting features from {file_path}: {e}")
        import traceback
        traceback.print_exc()
        return None

def train_model():
    """Train a model on the GTZAN dataset with improved parameters and evaluation"""
    print("Starting model training process...")
    start_time = time.time()

    # Path to the GTZAN dataset
    dataset_path = "E:/Data/genres_original"  # Path to the GTZAN dataset

    if not os.path.exists(dataset_path):
        print(f"Dataset not found at {dataset_path}, checking alternative locations...")

        # Try alternative locations with more options
        alt_paths = [
            "data/genres_original",
            "data/Data/genres_original",
            "data/gtzan/genres_original",
            "data/genres",
            os.path.join(os.getcwd(), "data/genres_original"),
            os.path.join(os.getcwd(), "data/genres")
        ]

        for path in alt_paths:
            if os.path.exists(path):
                dataset_path = path
                print(f"Dataset found at alternative location: {dataset_path}")
                break

        if dataset_path == "E:/Data/genres_original":
            print("Dataset not found in any location.")

            # Check if data directory exists
            if not os.path.exists("data"):
                print("Creating data directory...")
                os.makedirs("data", exist_ok=True)

            print("You may need to download the GTZAN dataset first.")
            print("Creating a simple model with synthetic data instead.")
            create_simple_model()
            return

    print(f"Using dataset from: {dataset_path}")
    features = []
    labels = []
    genre_counts = {genre: 0 for genre in GENRES}
    file_paths = []  # Store file paths for data augmentation

    # Process each genre folder
    for i, genre in enumerate(GENRES):
        genre_path = os.path.join(dataset_path, genre)

        # Skip if genre folder doesn't exist
        if not os.path.exists(genre_path):
            print(f"Warning: Genre folder {genre} not found at {genre_path}")
            continue

        # Process each audio file in the genre folder
        for file_path in glob.glob(os.path.join(genre_path, "*.wav")):
            print(f"Processing {file_path}")
            file_paths.append(file_path)

            # Extract features
            file_features = extract_features(file_path)

            if file_features is not None:
                features.append(file_features)
                labels.append(i)  # Use index as label
                genre_counts[genre] += 1

    # Data augmentation for underrepresented genres
    print("\nPerforming data augmentation for balanced classes...")
    max_samples = max(genre_counts.values())
    for genre, count in genre_counts.items():
        if count < max_samples:
            genre_idx = GENRES.index(genre)
            genre_features = [features[i] for i, label in enumerate(labels) if label == genre_idx]
            genre_indices = [i for i, label in enumerate(labels) if label == genre_idx]

            # Number of samples to add
            samples_to_add = max_samples - count
            print(f"Adding {samples_to_add} augmented samples for {genre}")

            # Add noise to existing features for data augmentation
            for _ in range(samples_to_add):
                # Randomly select a feature vector from this genre
                idx = np.random.choice(len(genre_features))
                base_feature = genre_features[idx]

                # Add random noise (5% variation)
                noise = np.random.normal(0, 0.05, size=base_feature.shape)
                augmented_feature = base_feature + noise * base_feature

                features.append(augmented_feature)
                labels.append(genre_idx)
                genre_counts[genre] += 1

    # Print dataset statistics
    print("\nDataset Statistics (after augmentation):")
    for genre, count in genre_counts.items():
        print(f"{genre}: {count} samples")

    # Convert to numpy arrays
    X = np.array(features)
    y = np.array(labels)

    print(f"\nTotal samples: {len(X)}")
    print(f"Feature vector size: {X.shape[1]}")

    # Split into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    print(f"Training set size: {len(X_train)}")
    print(f"Testing set size: {len(X_test)}")

    # Create multiple models for ensemble
    print("\nTraining ensemble of models...")

    # 1. Random Forest
    rf_model = Pipeline([
        ('scaler', StandardScaler()),
        ('classifier', RandomForestClassifier(
            n_estimators=300,
            max_depth=None,
            min_samples_split=2,
            min_samples_leaf=1,
            max_features='sqrt',
            bootstrap=True,
            class_weight='balanced',
            random_state=42,
            n_jobs=-1  # Use all available cores
        ))
    ])

    # 2. Gradient Boosting
    gb_model = Pipeline([
        ('scaler', StandardScaler()),
        ('classifier', GradientBoostingClassifier(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=5,
            min_samples_split=2,
            min_samples_leaf=1,
            subsample=0.8,
            random_state=42
        ))
    ])

    # Train the models
    print("Training Random Forest model...")
    rf_model.fit(X_train, y_train)

    print("Training Gradient Boosting model...")
    gb_model.fit(X_train, y_train)

    # Evaluate individual models
    rf_accuracy = rf_model.score(X_test, y_test)
    gb_accuracy = gb_model.score(X_test, y_test)

    print(f"\nRandom Forest accuracy: {rf_accuracy:.4f}")
    print(f"Gradient Boosting accuracy: {gb_accuracy:.4f}")

    # Create a voting ensemble
    from sklearn.ensemble import VotingClassifier

    # Create the ensemble with soft voting (using probabilities)
    ensemble = VotingClassifier(
        estimators=[
            ('rf', rf_model),
            ('gb', gb_model)
        ],
        voting='soft'
    )

    # Train the ensemble
    print("\nTraining the ensemble model...")
    ensemble.fit(X_train, y_train)

    # Evaluate the ensemble
    ensemble_accuracy = ensemble.score(X_test, y_test)
    print(f"Ensemble model accuracy: {ensemble_accuracy:.4f}")

    # Make predictions with the ensemble
    y_pred = ensemble.predict(X_test)

    # Print detailed classification report
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred, target_names=GENRES))

    # Generate confusion matrix
    cm = confusion_matrix(y_test, y_pred)

    # Plot confusion matrix
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=GENRES, yticklabels=GENRES)
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title('Confusion Matrix')

    # Save confusion matrix plot
    cm_dir = os.path.join("backend", "ml_model")
    os.makedirs(cm_dir, exist_ok=True)
    plt.savefig(os.path.join(cm_dir, "confusion_matrix.png"))

    # Display the confusion matrix on screen
    plt.show()

    # Save the model
    model_dir = os.path.join("backend", "ml_model")
    os.makedirs(model_dir, exist_ok=True)
    model_path = os.path.join(model_dir, "genre_classifier.pkl")

    # Save the ensemble model
    with open(model_path, 'wb') as f:
        pickle.dump(ensemble, f)

    print(f"\nEnsemble model saved to {model_path}")

    # Create a prediction cache directory
    cache_dir = os.path.join(model_dir, "prediction_cache")
    os.makedirs(cache_dir, exist_ok=True)

    end_time = time.time()
    print(f"\nTotal training time: {(end_time - start_time)/60:.2f} minutes")

def create_simple_model():
    """Create a simple model for genre classification with better defaults using ensemble approach"""
    print("Creating an enhanced simple model with ensemble approach...")

    # Create synthetic data that better represents audio features
    n_samples = 2000  # More samples for better training
    n_features = 80   # Match our enhanced feature extraction

    print(f"Generating synthetic data with {n_samples} samples and {n_features} features")

    # Create synthetic data with some structure for each genre
    X = np.zeros((n_samples, n_features))
    y = np.zeros(n_samples, dtype=int)

    samples_per_genre = n_samples // len(GENRES)

    # Create distinct feature patterns for each genre
    for i, genre in enumerate(GENRES):
        start_idx = i * samples_per_genre
        end_idx = (i + 1) * samples_per_genre

        # Base feature vector for this genre
        base_features = np.random.randn(n_features)

        # Create variations of the base features
        for j in range(start_idx, end_idx):
            # Add controlled noise to create variations
            noise = np.random.normal(0, 0.2, size=n_features)
            X[j] = base_features + noise
            y[j] = i

    # Split into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    print(f"Training set size: {len(X_train)}")
    print(f"Testing set size: {len(X_test)}")

    # Create multiple models for ensemble
    print("\nTraining ensemble of models...")

    # 1. Random Forest
    rf_model = Pipeline([
        ('scaler', StandardScaler()),
        ('classifier', RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            min_samples_split=2,
            min_samples_leaf=1,
            max_features='sqrt',
            bootstrap=True,
            random_state=42,
            n_jobs=-1
        ))
    ])

    # 2. Gradient Boosting
    gb_model = Pipeline([
        ('scaler', StandardScaler()),
        ('classifier', GradientBoostingClassifier(
            n_estimators=150,
            learning_rate=0.1,
            max_depth=5,
            min_samples_split=2,
            min_samples_leaf=1,
            subsample=0.8,
            random_state=42
        ))
    ])

    # Train the models
    print("Training Random Forest model...")
    rf_model.fit(X_train, y_train)

    print("Training Gradient Boosting model...")
    gb_model.fit(X_train, y_train)

    # Create a voting ensemble
    from sklearn.ensemble import VotingClassifier

    # Create the ensemble with soft voting (using probabilities)
    ensemble = VotingClassifier(
        estimators=[
            ('rf', rf_model),
            ('gb', gb_model)
        ],
        voting='soft'
    )

    # Train the ensemble
    print("Training the ensemble model...")
    ensemble.fit(X_train, y_train)

    # Evaluate the ensemble
    accuracy = ensemble.score(X_test, y_test)
    print(f"Ensemble model accuracy on synthetic data: {accuracy:.4f}")

    # Save the model
    model_dir = os.path.join("backend", "ml_model")
    os.makedirs(model_dir, exist_ok=True)
    model_path = os.path.join(model_dir, "genre_classifier.pkl")

    with open(model_path, 'wb') as f:
        pickle.dump(ensemble, f)

    # Create a prediction cache directory
    cache_dir = os.path.join(model_dir, "prediction_cache")
    os.makedirs(cache_dir, exist_ok=True)

    print(f"Enhanced ensemble model created and saved to {model_path}")

if __name__ == "__main__":
    train_model()