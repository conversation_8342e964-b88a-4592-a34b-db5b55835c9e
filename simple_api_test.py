import requests
import json
import os

def test_api():
    # Find a test file
    uploads_dir = 'backend/media/uploads'
    test_file = None
    
    for file in os.listdir(uploads_dir):
        if file.endswith(('.mp3', '.wav')):
            test_file = os.path.join(uploads_dir, file)
            break
    
    if not test_file:
        print("No test file found")
        return
    
    print(f"Testing with: {os.path.basename(test_file)}")
    
    # Test API
    with open(test_file, 'rb') as f:
        files = {'file': f}
        response = requests.post('http://localhost:8000/api/upload/', files=files)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print("API Response:")
            print(json.dumps(data, indent=2))
            
            # Check specifically for all_probabilities
            if 'all_probabilities' in data:
                print(f"\n✅ all_probabilities found with {len(data['all_probabilities'])} items")
                print("First 3 probabilities:")
                for i, prob in enumerate(data['all_probabilities'][:3]):
                    print(f"  {i+1}. {prob['genre']}: {prob['confidence']:.1%}")
            else:
                print("\n❌ all_probabilities NOT found")
                print("Available keys:", list(data.keys()))
        else:
            print(f"Error: {response.text}")

if __name__ == "__main__":
    test_api()
